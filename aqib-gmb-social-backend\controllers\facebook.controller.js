const logger = require("../utils/logger");
const Facebook = require("../models/facebook.models");
const FacebookService = require("../services/facebook.service");
const s3Service = require("../services/s3.service");

const facebookService = new FacebookService();

/**
 * Welcome endpoint for Facebook API
 */
const welcome = async (req, res) => {
  try {
    logger.logControllerAction("facebook", "welcome", req.requestId);

    res.status(200).json({
      success: true,
      message: "Facebook API is working",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Error in Facebook welcome", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Initiate Facebook authentication
 */
const authenticate = async (req, res) => {
  try {
    logger.logControllerAction("facebook", "authenticate", req.requestId);

    // Debug logging
    logger.info("Facebook authenticate request", {
      requestId: req.requestId,
      body: req.body,
      headers: req.headers,
      method: req.method,
      url: req.url,
    });

    const { userId } = req.body;

    logger.info("Extracted values", {
      requestId: req.requestId,
      userId,
      userIdType: typeof userId,
    });

    if (!userId) {
      logger.error("Missing required parameters", {
        requestId: req.requestId,
        userId,
        hasUserId: !!userId,
      });

      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    // Validate Facebook configuration
    if (!facebookService.validateConfig()) {
      return res.status(500).json({
        success: false,
        message: "Facebook configuration is incomplete",
      });
    }

    const authUrl = facebookService.generateAuthUrl(userId);

    logger.info("Facebook authentication URL generated", {
      requestId: req.requestId,
      userId,
      hasAuthUrl: !!authUrl,
    });

    res.status(200).json({
      success: true,
      authUrl: authUrl,
      message: "Facebook authentication URL generated successfully",
    });
  } catch (error) {
    logger.error("Error in Facebook authenticate", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Handle Facebook OAuth callback (GET - redirects to frontend)
 */
const callback = async (req, res) => {
  try {
    logger.logControllerAction("facebook", "callback", req.requestId);

    const { code, state, error, error_description } = req.query;

    // Handle OAuth errors
    if (error) {
      logger.error("Facebook OAuth error", {
        requestId: req.requestId,
        error,
        error_description,
      });

      const frontendUrl = process.env.FRONTEND_URL || "http://localhost:3000";
      return res.redirect(
        `${frontendUrl}/business-management/facebook/callback?error=${encodeURIComponent(
          error
        )}&error_description=${encodeURIComponent(error_description || "")}`
      );
    }

    if (!code || !state) {
      logger.error("Missing code or state in Facebook callback", {
        requestId: req.requestId,
        hasCode: !!code,
        hasState: !!state,
      });

      const frontendUrl = process.env.FRONTEND_URL || "http://localhost:3000";
      return res.redirect(
        `${frontendUrl}/business-management/facebook/callback?error=missing_parameters`
      );
    }

    // Redirect to frontend callback page with code and state
    const frontendUrl = process.env.FRONTEND_URL || "http://localhost:3000";
    res.redirect(
      `${frontendUrl}/business-management/facebook/callback?code=${encodeURIComponent(
        code
      )}&state=${encodeURIComponent(state)}`
    );
  } catch (error) {
    logger.error("Error in Facebook callback", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });

    const frontendUrl = process.env.FRONTEND_URL || "http://localhost:3000";
    res.redirect(
      `${frontendUrl}/business-management/facebook/callback?error=server_error`
    );
  }
};

/**
 * Handle Facebook OAuth callback validation (POST - internal API)
 */
const callbackValidation = async (req, res) => {
  try {
    logger.logControllerAction("facebook", "callbackValidation", req.requestId);

    const { code, state, userId } = req.body;

    if (!code || !state || !userId) {
      return res.status(400).json({
        success: false,
        message: "Authorization code, state, and userId are required",
      });
    }

    // Parse and validate state
    let stateData;
    try {
      stateData = JSON.parse(state);
    } catch (error) {
      logger.error("Invalid state parameter", {
        requestId: req.requestId,
        state,
        error: error.message,
      });
      return res.status(400).json({
        success: false,
        message: "Invalid state parameter",
      });
    }

    // Validate userId matches state
    if (stateData.userId !== userId) {
      logger.error("UserId mismatch", {
        requestId: req.requestId,
        requestUserId: userId,
        stateUserId: stateData.userId,
      });
      return res.status(400).json({
        success: false,
        message: "User ID mismatch",
      });
    }

    // Exchange code for access token
    const tokenData = await facebookService.exchangeCodeForToken(code);

    // Get Facebook user information
    const facebookUserInfo = await facebookService.getUserInfo(
      tokenData.access_token
    );

    // Get user pages
    const pages = await facebookService.getUserPages(tokenData.access_token);

    debugger;

    // Save OAuth tokens with Facebook user info
    const tokenResult = await Facebook.saveOAuthTokens({
      userId,
      facebookUserId: facebookUserInfo.id,
      facebookUserName: facebookUserInfo.name,
      facebookUserEmail: facebookUserInfo.email,
      facebookUserPicture: facebookUserInfo.picture?.data?.url,
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token || null,
      expiresAt: tokenData.expires_in
        ? new Date(Date.now() + tokenData.expires_in * 1000)
        : null,
    });

    // Get the inserted token ID for foreign key reference
    const facebookOAuthTokenId = tokenResult.result.insertId;

    // Save pages with reference to the oauth token
    const pagesData = pages.map((page) => ({
      facebookOAuthTokenId,
      pageId: page.id,
      pageName: page.name,
      pageAccessToken: page.access_token,
      pageCategory: page.category,
      pagePictureUrl: page.picture?.data?.url,
    }));

    await Facebook.savePages(pagesData);

    logger.info("Facebook authentication completed successfully", {
      requestId: req.requestId,
      userId,
      facebookUserId: facebookUserInfo.id,
      facebookUserEmail: facebookUserInfo.email,
      pagesCount: pages.length,
    });

    res.status(200).json({
      success: true,
      message: "Facebook authentication completed successfully",
      data: {
        facebookUser: {
          id: facebookUserInfo.id,
          name: facebookUserInfo.name,
          email: facebookUserInfo.email,
          picture: facebookUserInfo.picture?.data?.url,
        },
        pagesCount: pages.length,
        pages: pages.map((page) => ({
          id: page.id,
          name: page.name,
          category: page.category,
        })),
      },
    });
  } catch (error) {
    logger.error("Error in Facebook callback validation", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      message: "Internal server error during Facebook authentication",
      error: error.message,
    });
  }
};

/**
 * Get Facebook accounts for user
 */
const getFacebookAccounts = async (req, res) => {
  try {
    logger.logControllerAction(
      "facebook",
      "getFacebookAccounts",
      req.requestId
    );

    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    const result = await Facebook.getFacebookAccounts(parseInt(userId));

    if (result.success) {
      res.status(200).json({
        success: true,
        accounts: result.accounts,
        message: "Facebook accounts retrieved successfully",
      });
    } else {
      res.status(404).json({
        success: false,
        message: "No Facebook accounts found",
      });
    }
  } catch (error) {
    logger.error("Error in Facebook getFacebookAccounts", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Get Facebook pages for user
 */
const getPages = async (req, res) => {
  try {
    logger.logControllerAction("facebook", "getPages", req.requestId);

    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    const result = await Facebook.getPages(parseInt(userId));

    if (result.success) {
      res.status(200).json({
        success: true,
        pages: result.pages,
        message: "Facebook pages retrieved successfully",
      });
    } else {
      res.status(404).json({
        success: false,
        message: "No Facebook pages found",
      });
    }
  } catch (error) {
    logger.error("Error in Facebook getPages", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Create Facebook post
 */
const createPost = async (req, res) => {
  try {
    logger.logControllerAction("facebook", "createPost", req.requestId);

    const { userId } = req.params;
    const {
      pageId,
      message,
      description,
      link,
      media,
      published,
      scheduledPublishTime,
    } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    if (!pageId || !message) {
      return res.status(400).json({
        success: false,
        message: "Page ID and message are required",
      });
    }

    // Get page access token
    const pagesResult = await Facebook.getPages(parseInt(userId));
    const page = pagesResult.pages.find((p) => p.page_id === pageId);

    if (!page) {
      return res.status(404).json({
        success: false,
        message: "Facebook page not found",
      });
    }

    // Prepare post data for Facebook API
    const postData = {
      message: message,
    };

    // Add optional fields
    // Note: Facebook doesn't accept 'description' for regular posts
    // If there's a description, append it to the message
    if (description && description !== message) {
      postData.message = `${message}\n\n${description}`;
    }

    if (link) postData.link = link;
    if (scheduledPublishTime && !published) {
      postData.scheduled_publish_time = Math.floor(
        new Date(scheduledPublishTime).getTime() / 1000
      );
      postData.published = false;
    }

    // Log the post data being sent to Facebook
    logger.info("Sending post data to Facebook", {
      requestId: req.requestId,
      pageId,
      postData,
      hasMedia: media && media.length > 0,
    });

    // Handle media uploads if present
    let facebookPostResponse;
    if (media && media.length > 0) {
      // For now, handle single image posts
      const imageMedia = media.find((m) => m.type === "image");
      if (imageMedia) {
        // Create photo post with message
        const photoData = {
          url: imageMedia.url,
          caption: message,
        };

        // Don't include description for photo posts as it's not supported
        if (description && description !== message) {
          photoData.caption = `${message}\n\n${description}`;
        }

        facebookPostResponse = await facebookService.uploadPhoto(
          pageId,
          page.page_access_token,
          photoData
        );
      }
    } else {
      // Create text-only post
      facebookPostResponse = await facebookService.createPost(
        pageId,
        page.page_access_token,
        postData
      );
    }

    // Generate Facebook URL
    const facebookUrl = facebookService.generatePostUrl(
      pageId,
      facebookPostResponse.id
    );

    // Save post to database
    const saveResult = await Facebook.savePost({
      userId: parseInt(userId),
      pageId: pageId,
      facebookPostId: facebookPostResponse.id,
      postContent: req.body,
      postResponse: facebookPostResponse,
      message: message,
      description: description,
      link: link,
      published: published !== false,
      scheduledPublishTime: scheduledPublishTime || null,
      status: published !== false ? "published" : "scheduled",
      facebookUrl: facebookUrl,
    });

    if (saveResult.success) {
      logger.info("Facebook post created successfully", {
        requestId: req.requestId,
        userId,
        pageId,
        postId: facebookPostResponse.id,
        dbPostId: saveResult.postId,
      });

      res.status(200).json({
        success: true,
        message: "Facebook post created successfully",
        data: {
          id: saveResult.postId,
          facebookPostId: facebookPostResponse.id,
          pageId: pageId,
          message: message,
          description: description,
          link: link,
          facebookUrl: facebookUrl,
          status: published !== false ? "published" : "scheduled",
          createdTime: new Date().toISOString(),
          media: media,
        },
      });
    } else {
      res.status(500).json({
        success: false,
        message: "Failed to save post to database",
      });
    }
  } catch (error) {
    logger.error("Error in Facebook createPost", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
      facebookError: error.response?.data,
      statusCode: error.response?.status,
    });

    // Provide more specific error messages based on Facebook API response
    let errorMessage = "Internal server error";
    if (error.response?.data?.error) {
      const fbError = error.response.data.error;
      errorMessage = `Facebook API Error: ${
        fbError.message || fbError.error_user_msg || "Unknown error"
      }`;

      // Handle specific Facebook error codes
      if (fbError.code === 190) {
        errorMessage =
          "Facebook access token is invalid or expired. Please reconnect your Facebook account.";
      } else if (fbError.code === 200) {
        errorMessage =
          "Facebook permissions error. Please check your page permissions.";
      } else if (fbError.code === 100) {
        errorMessage =
          "Invalid Facebook API request. Please check your post content.";
      }
    }

    res.status(500).json({
      success: false,
      message: errorMessage,
      error: error.message,
      facebookError: error.response?.data?.error,
    });
  }
};

/**
 * Data deletion instructions endpoint (public)
 */
const dataDeletionInstructions = async (req, res) => {
  try {
    logger.logControllerAction(
      "facebook",
      "dataDeletionInstructions",
      req.requestId
    );

    const instructions = {
      title: "Facebook Data Deletion Instructions",
      description:
        "Instructions for users to request deletion of their data collected through our Facebook integration",
      dataCollected: [
        "Basic profile information (name, email)",
        "Facebook Page information you manage",
        "Posts and content you create through our platform",
        "Access tokens for Facebook API integration",
      ],
      steps: [
        "Contact our support team by sending an email to our data protection team",
        "Provide identification including your email address and Facebook account information",
        "Specify deletion scope - let us know if you want to delete all data or specific information",
        "We will confirm your identity and process your request within 30 days",
      ],
      contact: {
        email: "<EMAIL>",
        subject: "Facebook Data Deletion Request",
        responseTime: "We will respond to your request within 30 days",
      },
      importantNotes: [
        "Data deletion is permanent and cannot be undone",
        "You will need to reconnect your Facebook account if you wish to use our services again",
        "Some data may be retained for legal or security purposes as required by law",
      ],
      compliance:
        "This endpoint complies with Facebook's Platform Policy requirements for data deletion instructions",
      lastUpdated: new Date().toISOString(),
    };

    logger.info("Facebook data deletion instructions accessed", {
      requestId: req.requestId,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.headers["user-agent"],
    });

    res.status(200).json({
      success: true,
      message: "Facebook data deletion instructions retrieved successfully",
      instructions: instructions,
    });
  } catch (error) {
    logger.error("Error in Facebook data deletion instructions", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

module.exports = {
  welcome,
  authenticate,
  callback,
  callbackValidation,
  getFacebookAccounts,
  getPages,
  createPost,
  dataDeletionInstructions,
};

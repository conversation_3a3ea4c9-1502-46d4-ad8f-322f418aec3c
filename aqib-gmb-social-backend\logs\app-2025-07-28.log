{"timestamp":"2025-07-28T13:51:15.883Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-28T13:51:16.014Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-28T13:51:21.497Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-28T08:21:21.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-28T13:52:43.825Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"bf9c249d-7f4a-43c8-86bf-8c4934a11403","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-28T13:52:43.840Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"2fc9bc8b-1b19-460e-83d7-57d7e3f83463","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-28T13:52:43.845Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"2fc9bc8b-1b19-460e-83d7-57d7e3f83463","userId":"132"}
{"timestamp":"2025-07-28T13:52:43.861Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8b626a47-79ad-417c-904b-efcfc39de217","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-28T13:52:43.875Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"3458f5e1-e9b2-47c7-a8e5-f699e2fa7278","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-28T13:52:44.000Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-28T13:52:44.011Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-28T13:52:44.014Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"3458f5e1-e9b2-47c7-a8e5-f699e2fa7278","userId":"132","accountCount":0}
{"timestamp":"2025-07-28T13:52:44.020Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-28T13:52:44.032Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-28T13:54:22.804Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"dda486de-ceea-4857-825f-59569e830958","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-28T13:54:22.807Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"dda486de-ceea-4857-825f-59569e830958","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.eNOguet4WBkKaZo4nM_3PR3mmXeZPjNnRg7iaOu4FqY","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.eNOguet4WBkKaZo4nM_3PR3mmXeZPjNnRg7iaOu4FqY","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-28T13:54:22.811Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"dda486de-ceea-4857-825f-59569e830958","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-28T13:54:22.817Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-28T13:54:22.820Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"dda486de-ceea-4857-825f-59569e830958","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-28T13:57:05.528Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"4cdb132a-c44b-4c18-97f3-84d1996d285c","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-28T13:57:05.547Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"eab24cbe-b1da-4598-9ee1-f933a38515fe","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-28T13:57:05.677Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"fba82c86-c9db-4ebf-af0e-4258074123ac","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-28T13:57:05.721Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d1c4b226-5082-463e-b53e-d607c03c69e8","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-28T13:57:05.731Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"d1c4b226-5082-463e-b53e-d607c03c69e8","userId":"132"}
{"timestamp":"2025-07-28T13:57:05.750Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-28T13:57:05.777Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-28T13:57:05.820Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-28T13:57:05.824Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"fba82c86-c9db-4ebf-af0e-4258074123ac","userId":"132","accountCount":0}
{"timestamp":"2025-07-28T13:57:05.860Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-28T13:57:53.143Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e92d9c3e-0dcd-4ae3-ab38-6373fac8fe8a","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-28T13:57:53.145Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"e92d9c3e-0dcd-4ae3-ab38-6373fac8fe8a","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.eNOguet4WBkKaZo4nM_3PR3mmXeZPjNnRg7iaOu4FqY","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.eNOguet4WBkKaZo4nM_3PR3mmXeZPjNnRg7iaOu4FqY","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-28T13:57:53.149Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"e92d9c3e-0dcd-4ae3-ab38-6373fac8fe8a","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-28T13:57:53.152Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-28T13:57:53.155Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"e92d9c3e-0dcd-4ae3-ab38-6373fac8fe8a","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-28T13:58:34.343Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-28T13:58:34.426Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-28T13:58:36.447Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-28T08:28:36.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-28T13:58:54.600Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-28T13:58:54.681Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-28T13:58:56.037Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-28T08:28:56.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-28T13:59:09.681Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ec42bd52-1272-472a-90f3-480f2f87e2bc","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-28T13:59:09.684Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"ec42bd52-1272-472a-90f3-480f2f87e2bc","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.eNOguet4WBkKaZo4nM_3PR3mmXeZPjNnRg7iaOu4FqY","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.eNOguet4WBkKaZo4nM_3PR3mmXeZPjNnRg7iaOu4FqY","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-28T13:59:09.690Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"ec42bd52-1272-472a-90f3-480f2f87e2bc","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-28T13:59:09.692Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-28T13:59:09.695Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"ec42bd52-1272-472a-90f3-480f2f87e2bc","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-28T14:00:31.604Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-28T14:00:31.753Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-28T14:00:34.147Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-28T08:30:33.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-28T16:02:36.685Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"a144978e-accd-4f6c-8297-25ce9ca79f06","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-28T16:02:36.690Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"a144978e-accd-4f6c-8297-25ce9ca79f06","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.eNOguet4WBkKaZo4nM_3PR3mmXeZPjNnRg7iaOu4FqY","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.eNOguet4WBkKaZo4nM_3PR3mmXeZPjNnRg7iaOu4FqY","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-28T16:02:36.694Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"a144978e-accd-4f6c-8297-25ce9ca79f06","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-28T16:02:36.698Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-28T16:02:36.705Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"a144978e-accd-4f6c-8297-25ce9ca79f06","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-28T16:13:03.017Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-28T16:13:03.093Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-28T16:13:04.818Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-28T10:43:04.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-28T16:14:24.007Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-28T16:14:24.090Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-28T16:14:25.660Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-28T10:44:25.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-28T16:14:52.419Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-28T16:14:52.506Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-28T16:14:54.053Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-28T10:44:53.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-28T16:25:17.708Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-28T16:25:17.848Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-28T16:25:20.560Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-28T10:55:20.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-28T16:25:28.123Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"539ca8f9-cfe2-4430-abfe-c3d1dfd3b668","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-28T16:25:28.128Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"539ca8f9-cfe2-4430-abfe-c3d1dfd3b668","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.eNOguet4WBkKaZo4nM_3PR3mmXeZPjNnRg7iaOu4FqY","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.eNOguet4WBkKaZo4nM_3PR3mmXeZPjNnRg7iaOu4FqY","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-28T16:25:28.132Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"539ca8f9-cfe2-4430-abfe-c3d1dfd3b668","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-28T16:25:28.135Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-28T16:25:28.138Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"539ca8f9-cfe2-4430-abfe-c3d1dfd3b668","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-28T16:31:33.620Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-28","endDate":"2025-07-28"}
{"timestamp":"2025-07-28T16:31:33.726Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-28T16:31:34.913Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"2ec50451-0867-4460-9b2e-a663bde5c4b5","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-28T16:31:34.944Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"7fde1e01-586c-4598-ba77-2509ebceaee5","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-28T16:31:34.947Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"7fde1e01-586c-4598-ba77-2509ebceaee5","userId":"132"}
{"timestamp":"2025-07-28T16:31:34.962Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"835dd129-bc17-4909-9d04-7e7e4eb411b7","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-28T16:31:34.983Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"03cb085f-be4b-4110-9f34-4c4c696500db","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-28T16:31:34.997Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-28T16:31:35.093Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-28T16:31:35.095Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"03cb085f-be4b-4110-9f34-4c4c696500db","userId":"132","accountCount":0}
{"timestamp":"2025-07-28T16:31:35.104Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-28T16:31:35.115Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-28T16:31:57.471Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e7c5a474-4e75-48ba-a44a-7af79e9b5106","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-28T16:31:57.475Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"e7c5a474-4e75-48ba-a44a-7af79e9b5106","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-28T16:31:57.636Z","level":"WARN","message":"Login failed - invalid credentials","environment":"DEVELOPMENT","requestId":"e7c5a474-4e75-48ba-a44a-7af79e9b5106","email":"<EMAIL>","reason":"Incorrect password."}
{"timestamp":"2025-07-28T16:32:01.648Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"7fc72e48-afac-4310-95a9-94381697282e","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-28T16:32:01.651Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"7fc72e48-afac-4310-95a9-94381697282e","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-28T16:32:01.808Z","level":"WARN","message":"Login failed - invalid credentials","environment":"DEVELOPMENT","requestId":"7fc72e48-afac-4310-95a9-94381697282e","email":"<EMAIL>","reason":"Incorrect password."}
{"timestamp":"2025-07-28T16:32:15.220Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"fdd7f3e2-0589-4fa8-a35d-16a8934a1c14","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-28T16:32:15.225Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"fdd7f3e2-0589-4fa8-a35d-16a8934a1c14","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-28T16:32:15.368Z","level":"WARN","message":"Login failed - invalid credentials","environment":"DEVELOPMENT","requestId":"fdd7f3e2-0589-4fa8-a35d-16a8934a1c14","email":"<EMAIL>","reason":"Incorrect password."}
{"timestamp":"2025-07-28T16:33:23.598Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"43cba2f8-635d-4d80-b649-40f97819eaed","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-28T16:33:23.603Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"43cba2f8-635d-4d80-b649-40f97819eaed","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-28T16:33:23.751Z","level":"WARN","message":"Login failed - invalid credentials","environment":"DEVELOPMENT","requestId":"43cba2f8-635d-4d80-b649-40f97819eaed","email":"<EMAIL>","reason":"Incorrect password."}
{"timestamp":"2025-07-28T16:33:36.366Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"0160994f-3e7b-435f-9b15-1b677a37b26c","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-28T16:33:36.370Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"0160994f-3e7b-435f-9b15-1b677a37b26c","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-28T16:33:36.509Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"0160994f-3e7b-435f-9b15-1b677a37b26c","userId":52,"email":"<EMAIL>"}
{"timestamp":"2025-07-28T16:33:46.663Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"1a6d3da6-52ce-4b5c-b248-e2289d43b645","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-28T16:33:46.712Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d47ebb0d-8a12-4524-8f6e-3d889f4aac7d","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-28T16:33:46.716Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"d47ebb0d-8a12-4524-8f6e-3d889f4aac7d","userId":"52"}
{"timestamp":"2025-07-28T16:33:46.730Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"5ca630f0-d9ab-424c-af51-86fd463bb680","controller":"twitter","action":"getTwitterAccounts","userId":"52"}
{"timestamp":"2025-07-28T16:33:46.749Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6207b149-3ea4-480c-842c-96d9c1d456f0","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-28T16:33:46.756Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":52,"pagesCount":1}
{"timestamp":"2025-07-28T16:33:46.765Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":52,"accountsCount":0}
{"timestamp":"2025-07-28T16:33:46.770Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":52,"accountCount":0}
{"timestamp":"2025-07-28T16:33:46.773Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"5ca630f0-d9ab-424c-af51-86fd463bb680","userId":"52","accountCount":0}
{"timestamp":"2025-07-28T16:33:46.787Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":52,"accountsCount":0}

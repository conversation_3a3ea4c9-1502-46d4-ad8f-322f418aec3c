import React, { useState, useEffect, useContext, useRef } from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Button,
  Alert,
  Tabs,
  Tab,
  CircularProgress,
  useTheme,
  useMediaQuery,
  Modal,
  IconButton,
  Stack,
} from "@mui/material";
import {
  Close as CloseIcon,
  Print as PrintIcon,
  Map as MapIcon,
} from "@mui/icons-material";
import html2canvas from "html2canvas";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import LocalFalconControls from "../../components/localFalcon/LocalFalconControls.component";
import LocalFalconMap from "../../components/localFalcon/LocalFalconMap.component";
import LocalFalconDashboard from "../../components/localFalcon/LocalFalconDashboard.component";
import LocalFalconService, {
  LocalFalconConfiguration,
  LocalFalconSearchRequest,
  LocalFalconScanRequest,
  LocalFalconGridPoint,
  LocalFalconRankingResult,
  LocalFalconScanResult,
  LocalFalconBusiness,
  LocalFalconTrendData,
  LocalFalconAlert,
} from "../../services/localFalcon/localFalcon.service";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import "./localFalcon.screen.style.css";

interface LocalFalconScreenProps {
  title: string;
}

interface LocationData {
  name: string;
  lat: number;
  lng: number;
  address: string;
  placeId: string;
}

const LocalFalconScreen: React.FC<LocalFalconScreenProps> = ({ title }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [localFalconService] = useState(new LocalFalconService(dispatch));
  const [loading, setLoading] = useState(false);

  // Toast context for notifications
  const { setToastConfig } = useContext(ToastContext);

  // Responsive and height matching refs
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const controlsPanelRef = useRef<HTMLDivElement>(null);
  const [mapHeight, setMapHeight] = useState(600);

  // State management
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(
    null
  );
  const [gridPoints, setGridPoints] = useState<LocalFalconGridPoint[]>([]);
  const [rankingResults, setRankingResults] = useState<
    LocalFalconRankingResult[]
  >([]);
  const [scanResult, setScanResult] = useState<LocalFalconScanResult | null>(
    null
  );

  const [trendData, setTrendData] = useState<LocalFalconTrendData[]>([]);
  const [alerts, setAlerts] = useState<LocalFalconAlert[]>([]);

  const [configuration, setConfiguration] = useState<LocalFalconConfiguration>({
    name: "",
    keyword: "",
    businessName: "",
    placeId: "",
    centerLat: 0,
    centerLng: 0,
    gridSize: "5x5",
    radius: 1,
    unit: "kilometers",
    isScheduleEnabled: false,
    settings: {},
  });

  // UI state
  const [activeTab, setActiveTab] = useState(0);
  const [showHeatMap, setShowHeatMap] = useState(false);
  const [isMapModalOpen, setIsMapModalOpen] = useState(false);
  const [savedConfigurations, setSavedConfigurations] = useState<
    LocalFalconConfiguration[]
  >([]);

  const user = useSelector((state: any) => state.authReducer?.userInfo);

  useEffect(() => {
    document.title = title;
    if (user?.id) {
      loadSavedConfigurations();
    }
  }, [title, user]);

  // Effect to calculate map height based on controls panel height
  useEffect(() => {
    const calculateMapHeight = () => {
      if (controlsPanelRef.current && !isMobile) {
        // Get the most accurate height measurement
        const boundingRect = controlsPanelRef.current.getBoundingClientRect();
        const controlsHeight = Math.ceil(boundingRect.height);

        // Set the exact height to match the controls panel
        setMapHeight(Math.max(controlsHeight, 500));
      } else {
        // Default height for mobile or when controls ref is not available
        const defaultHeight = isMobile ? 400 : 600;
        setMapHeight(defaultHeight);
      }
    };

    // Calculate with multiple delays to ensure DOM is ready
    const timeouts = [100, 300, 500, 1000];
    const timeoutIds = timeouts.map((delay) =>
      setTimeout(calculateMapHeight, delay)
    );

    // Recalculate on window resize
    const handleResize = () => {
      setTimeout(calculateMapHeight, 100);
    };

    window.addEventListener("resize", handleResize);

    // Also recalculate when controls content changes
    const observer = new MutationObserver(() => {
      setTimeout(calculateMapHeight, 100);
    });

    if (controlsPanelRef.current) {
      observer.observe(controlsPanelRef.current, {
        childList: true,
        subtree: true,
        attributes: true,
      });
    }

    return () => {
      timeoutIds.forEach((id) => clearTimeout(id));
      window.removeEventListener("resize", handleResize);
      observer.disconnect();
    };
  }, [isMobile, savedConfigurations, alerts]);

  const loadSavedConfigurations = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const response = await localFalconService.getConfigurations(user.id);
      if (response.success) {
        setSavedConfigurations(response.data);
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        "Failed to load saved configurations",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const handleLocationSearch = async (
    searchRequest: LocalFalconSearchRequest
  ) => {
    try {
      setLoading(true);

      const response = await localFalconService.searchPlaces(searchRequest);

      if (response.success && response.data.length > 0) {
        const locationData = response.data[0];
        setCurrentLocation({
          name: locationData.name,
          lat: locationData.lat,
          lng: locationData.lng,
          address: locationData.address,
          placeId: locationData.placeId,
        });

        setConfiguration((prev) => ({
          ...prev,
          centerLat: locationData.lat,
          centerLng: locationData.lng,
        }));

        // Auto-generate grid when location is found
        await generateGrid(locationData.lat, locationData.lng);

        setToastConfig(
          ToastSeverity.Success,
          "Location found and grid generated successfully!",
          true
        );
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to search location",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const generateGrid = async (centerLat?: number, centerLng?: number) => {
    try {
      setLoading(true);

      const lat = centerLat || configuration.centerLat;
      const lng = centerLng || configuration.centerLng;

      if (!lat || !lng) {
        throw new Error("Center coordinates are required");
      }

      const response = await localFalconService.calculateGrid({
        lat,
        lng,
        gridSize: configuration.gridSize,
        radius: configuration.radius,
        unit: configuration.unit,
      });

      if (response.success) {
        setGridPoints(response.data.gridPoints);
        setToastConfig(
          ToastSeverity.Success,
          "Grid generated successfully!",
          true
        );
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to generate grid",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const handleScan = async (scanRequest: LocalFalconScanRequest) => {
    try {
      setLoading(true);

      const response = await localFalconService.runGridScan(scanRequest);

      if (response.success) {
        const result = response.data;
        setScanResult(result);
        setRankingResults(result.rankings);
        setGridPoints(result.gridPoints);

        setToastConfig(
          ToastSeverity.Success,
          `Scan completed! Average position: ${result.averagePosition.toFixed(
            1
          )}, Visibility: ${result.visibilityPercentage.toFixed(1)}%`,
          true
        );

        // Keep user on current tab instead of auto-switching
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to run scan",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const handleConfigurationChange = (
    updates: Partial<LocalFalconConfiguration>
  ) => {
    setConfiguration((prev) => ({ ...prev, ...updates }));

    // Update currentLocation if coordinates are provided (e.g., when business is selected)
    if (updates.centerLat && updates.centerLng) {
      const newLocation = {
        name:
          updates.businessName || currentLocation?.name || "Selected Location",
        lat: updates.centerLat,
        lng: updates.centerLng,
        address: updates.businessName || currentLocation?.address || "",
        placeId: updates.placeId || currentLocation?.placeId || "",
      };

      setCurrentLocation(newLocation);
    }
  };

  const handleSaveConfiguration = async () => {
    try {
      setLoading(true);

      if (!user?.id) {
        throw new Error("Please log in to save configurations");
      }

      if (!configuration.name.trim()) {
        throw new Error("Configuration name is required");
      }

      const configToSave = {
        ...configuration,
        userId: user.id,
      };

      const response = await localFalconService.saveConfiguration(configToSave);

      if (response.success) {
        setToastConfig(
          ToastSeverity.Success,
          "Configuration saved successfully!",
          true
        );
        await loadSavedConfigurations();
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to save configuration",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const handleLoadConfiguration = async (config: LocalFalconConfiguration) => {
    try {
      setLoading(true);

      setConfiguration(config);
      setCurrentLocation({
        name: config.businessName || "Loaded Location",
        lat: config.centerLat,
        lng: config.centerLng,
        address: config.businessName || "",
        placeId: config.placeId || "",
      });

      // Load trend data for this configuration
      if (config.id) {
        const trendResponse = await localFalconService.getTrendData(
          config.id.toString()
        );
        if (trendResponse.success) {
          setTrendData(trendResponse.data);
        }
      }

      await generateGrid(config.centerLat, config.centerLng);

      setToastConfig(
        ToastSeverity.Success,
        "Configuration loaded successfully!",
        true
      );
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to load configuration",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteConfiguration = async (configId: number) => {
    try {
      setLoading(true);

      const response = await localFalconService.deleteConfiguration(
        configId.toString()
      );

      if (response.success) {
        setToastConfig(
          ToastSeverity.Success,
          "Configuration deleted successfully!",
          true
        );
        await loadSavedConfigurations();
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to delete configuration",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  // Handle map modal functions
  const handleOpenMapModal = () => {
    setIsMapModalOpen(true);
  };

  const handleCloseMapModal = () => {
    setIsMapModalOpen(false);
  };

  const handlePrintMap = async () => {
    const mapElement = document.getElementById("modal-map-container");
    if (mapElement) {
      try {
        const canvas = await html2canvas(mapElement, {
          useCORS: true,
          allowTaint: true,
          scale: 2,
          width: mapElement.scrollWidth,
          height: mapElement.scrollHeight,
        });

        // Create download link
        const link = document.createElement("a");
        link.download = `local-falcon-map-${
          new Date().toISOString().split("T")[0]
        }.png`;
        link.href = canvas.toDataURL();
        link.click();
      } catch (error) {
        console.error("Error generating map image:", error);
        setToastConfig(
          ToastSeverity.Error,
          "Failed to generate map image. Please try again.",
          true
        );
      }
    }
  };

  // Load sample scan data for testing
  const loadSampleData = async () => {
    try {
      setLoading(true);

      // Sample data from the JSON file
      const sampleScanResult: LocalFalconScanResult = {
        keyword: "eye hospital",
        businessName: "Sri Eye Care Speciality Eye Hospital",
        gridConfiguration: {
          lat: 13.0196844,
          lng: 77.6285592,
          gridSize: "5x5",
          radius: 1,
          unit: "kilometers",
        },
        gridPoints: [],
        rankings: [],
        averagePosition: 2.09,
        visibilityPercentage: 88,
        totalSearches: 25,
        foundInResults: 22,
        rawResponse: {
          code: 200,
          success: true,
          parameters: {
            keyword: "eye hospital",
            lat: "13.0196844",
            lng: "77.6285592",
            grid_size: "5",
            radius: "1",
            measurement: "km",
            place_id: "ChIJceJaLR8XrjsRLtW3QoeKOxc",
          },
          data: {
            points: 25,
            found: 22,
            percent: 88,
            arp: 2.090909090909091,
            atrp: 6.88,
            solv: 72,
            results: [
              {
                lat: 13.010701247158805,
                lng: 77.61933902148155,
                found: true,
                rank: 3,
                count: 20,
                results: [
                  {
                    rank: 1,
                    place_id: "ChIJFULDvvMWrjsRcGgSqCCXsUI",
                    business: "Dr Agarwals Eye Hospital",
                    address:
                      "33, Coles Rd, opposite Bata Showroom, Cleveland Town, Frazer Town, Bengaluru, Karnataka 560005",
                    rating: 4.8,
                    reviews: 5499,
                    lat: 12.9962226,
                    lng: 77.6127119,
                  },
                  {
                    rank: 3,
                    place_id: "ChIJceJaLR8XrjsRLtW3QoeKOxc",
                    business: "Sri Eye Care Speciality Eye Hospital",
                    address:
                      "HBR Layout 2nd Block, Stage 1, HBR Layout, Bengaluru, Karnataka 560043",
                    rating: 4.8,
                    reviews: 261,
                    lat: 13.0196844,
                    lng: 77.6285592,
                  },
                ],
              },
              {
                lat: 13.015192823579401,
                lng: 77.61933902148155,
                found: false,
                rank: false,
                count: 20,
                results: [],
              },
              {
                lat: 13.0196844,
                lng: 77.61933902148155,
                found: true,
                rank: 1,
                count: 20,
                results: [
                  {
                    rank: 1,
                    place_id: "ChIJceJaLR8XrjsRLtW3QoeKOxc",
                    business: "Sri Eye Care Speciality Eye Hospital",
                    address:
                      "HBR Layout 2nd Block, Stage 1, HBR Layout, Bengaluru, Karnataka 560043",
                    rating: 4.8,
                    reviews: 261,
                    lat: 13.0196844,
                    lng: 77.6285592,
                  },
                ],
              },
            ],
          },
        },
      };

      // Set the sample data
      setScanResult(sampleScanResult);
      setCurrentLocation({
        name: sampleScanResult.businessName,
        lat: sampleScanResult.gridConfiguration.lat,
        lng: sampleScanResult.gridConfiguration.lng,
        address:
          "HBR Layout 2nd Block, Stage 1, HBR Layout, Bengaluru, Karnataka 560043",
        placeId: "ChIJceJaLR8XrjsRLtW3QoeKOxc",
      });

      setToastConfig(
        ToastSeverity.Success,
        "Sample scan data loaded successfully!",
        true
      );

      // Keep user on current tab instead of auto-switching
    } catch (error: any) {
      setToastConfig(ToastSeverity.Error, "Failed to load sample data", true);
    } finally {
      setLoading(false);
    }
  };

  // Show login message if user is not authenticated
  if (!user?.id) {
    return (
      <LeftMenuComponent>
        <Box>
          <Box sx={{ marginBottom: "5px" }}>
            <h3 className="pageTitle">Local Falcon</h3>
            <Typography variant="subtitle2" className="subtitle2">
              Advanced local search ranking analysis and competitor tracking
            </Typography>
          </Box>

          <Alert
            severity="warning"
            sx={{ mt: 2 }}
            action={
              <Button
                color="inherit"
                size="small"
                onClick={() => navigate("/")}
                variant="outlined"
              >
                Login
              </Button>
            }
          >
            Please log in to access Local Falcon functionality. This feature
            requires authentication to save configurations and track ranking
            history.
          </Alert>
        </Box>
      </LeftMenuComponent>
    );
  }

  return (
    <LeftMenuComponent>
      <Box>
        <Box sx={{ marginBottom: "5px" }}>
          <h3 className="pageTitle">Local Falcon</h3>
          <Typography variant="subtitle2" className="subtitle2">
            Advanced local search ranking analysis and competitor tracking
          </Typography>
        </Box>

        {/* Tab Navigation */}
        <Box sx={{ borderBottom: 1, borderColor: "divider", mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={(_, newValue) => setActiveTab(newValue)}
          >
            <Tab className="text-capitalize" label="Search & Scan" />
            <Tab className="text-capitalize" label="Analytics Dashboard" />
          </Tabs>
        </Box>

        {/* Sample Data Button for Testing */}
        <Box sx={{ mb: 2, display: "flex", gap: 2, flexWrap: "wrap" }}>
          {/* <Button
            variant="outlined"
            color="secondary"
            onClick={loadSampleData}
            disabled={loading}
            size="small"
            className="text-capitalize border_raduis"
          >
            Load Sample Scan Data (Demo)
          </Button> */}
          {/* <Button
            variant="outlined"
            color="primary"
            onClick={() => {
              if (controlsPanelRef.current) {
                const boundingRect =
                  controlsPanelRef.current.getBoundingClientRect();
                const controlsHeight = Math.ceil(boundingRect.height);
                const newHeight = Math.max(controlsHeight, 500);
                console.log(
                  "Manual sync - Controls height:",
                  controlsHeight,
                  "Map height:",
                  newHeight
                );
                setMapHeight(newHeight);
              }
            }}
            size="small"
            className="text-capitalize border_raduis"
          >
            Sync Heights
          </Button> */}
        </Box>

        {activeTab === 0 && (
          <Grid container spacing={3} className="local-falcon-container">
            {/* Controls Panel */}
            <Grid item xs={12} md={4}>
              <Box
                ref={controlsPanelRef}
                className="local-falcon-controls-panel"
              >
                <LocalFalconControls
                  onSearch={handleLocationSearch}
                  onScan={handleScan}
                  onSaveConfiguration={handleSaveConfiguration}
                  loading={loading}
                  currentLocation={currentLocation}
                  savedConfigurations={savedConfigurations}
                  onLoadConfiguration={handleLoadConfiguration}
                  onDeleteConfiguration={handleDeleteConfiguration}
                  configuration={configuration}
                  onConfigurationChange={handleConfigurationChange}
                  localFalconService={localFalconService}
                />
              </Box>
            </Grid>

            {/* Map Panel */}
            <Grid item xs={12} md={8}>
              <LocalFalconMap
                center={
                  currentLocation
                    ? { lat: currentLocation.lat, lng: currentLocation.lng }
                    : null
                }
                gridPoints={gridPoints}
                rankingResults={rankingResults}
                scanResult={scanResult}
                loading={loading}
                showHeatMap={showHeatMap}
                onShowHeatMapChange={setShowHeatMap}
              />
            </Grid>
          </Grid>
        )}

        {activeTab === 1 && (
          <Paper sx={{ p: 1 }}>
            {scanResult && (
              <Box sx={{ mb: 2, display: "flex", justifyContent: "flex-end" }}>
                <Button
                  variant="contained"
                  startIcon={<MapIcon />}
                  onClick={handleOpenMapModal}
                  sx={{
                    backgroundColor: "primary.main",
                    "&:hover": {
                      backgroundColor: "primary.dark",
                    },
                  }}
                >
                  View Map
                </Button>
              </Box>
            )}
            <LocalFalconDashboard
              scanResult={scanResult}
              trendData={trendData}
              alerts={alerts}
              competitors={[]}
              loading={loading}
            />
          </Paper>
        )}

        {/* Map Modal */}
        <Modal
          open={isMapModalOpen}
          onClose={handleCloseMapModal}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Box
            id="modal-map-container"
            sx={{
              width: "90vw",
              height: "90vh",
              bgcolor: "background.paper",
              borderRadius: 2,
              boxShadow: 24,
              position: "relative",
              overflow: "hidden",
            }}
          >
            {/* Modal Header */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                p: 2,
                borderBottom: 1,
                borderColor: "divider",
                bgcolor: "primary.main",
                color: "primary.contrastText",
              }}
            >
              <Typography variant="h6" fontWeight="bold">
                Local Falcon Map - {scanResult?.businessName}
              </Typography>
              <Stack direction="row" spacing={1}>
                <IconButton
                  onClick={handlePrintMap}
                  sx={{ color: "primary.contrastText" }}
                  title="Download as Image"
                >
                  <PrintIcon />
                </IconButton>
                <IconButton
                  onClick={handleCloseMapModal}
                  sx={{ color: "primary.contrastText" }}
                  title="Close"
                >
                  <CloseIcon />
                </IconButton>
              </Stack>
            </Box>

            {/* Modal Content */}
            <Box
              sx={{
                height: "calc(100% - 64px)",
                display: "flex",
                position: "relative",
                overflow: "hidden",
              }}
            >
              {scanResult && (
                <Box
                  sx={{
                    width: "100%",
                    height: "100%",
                    position: "relative",
                    "& .local-falcon-map-container": {
                      height: "100% !important",
                      width: "100% !important",
                      position: "relative !important",
                    },
                    "& .local-falcon-controls": {
                      position: "absolute !important",
                      top: "16px !important",
                      left: "16px !important",
                      right: "auto !important",
                      zIndex: "1001 !important",
                      backgroundColor: "rgba(255, 255, 255, 0.98) !important",
                      borderRadius: "8px !important",
                      padding: "12px !important",
                      boxShadow: "0 4px 20px rgba(0,0,0,0.15) !important",
                      maxWidth: "280px !important",
                      border: "1px solid rgba(0,0,0,0.1) !important",
                      display: "block !important",
                      visibility: "visible !important",
                      "& > *": {
                        display: "flex !important",
                        flexDirection: "column !important",
                        gap: "8px !important",
                      },
                      "& .MuiChip-root": {
                        display: "flex !important",
                        visibility: "visible !important",
                        opacity: "1 !important",
                        minHeight: "32px !important",
                        margin: "2px 0 !important",
                      },
                      "& .MuiFormControlLabel-root": {
                        display: "flex !important",
                        visibility: "visible !important",
                        margin: "4px 0 !important",
                      },
                      "& .MuiStack-root": {
                        display: "flex !important",
                        flexDirection: "column !important",
                        gap: "8px !important",
                      },
                    },
                  }}
                >
                  <LocalFalconMap
                    center={
                      currentLocation
                        ? { lat: currentLocation.lat, lng: currentLocation.lng }
                        : null
                    }
                    gridPoints={gridPoints}
                    rankingResults={rankingResults}
                    scanResult={scanResult}
                    loading={loading}
                    showHeatMap={showHeatMap}
                    onShowHeatMapChange={setShowHeatMap}
                  />
                </Box>
              )}
            </Box>
          </Box>
        </Modal>
      </Box>
    </LeftMenuComponent>
  );
};

export default LocalFalconScreen;

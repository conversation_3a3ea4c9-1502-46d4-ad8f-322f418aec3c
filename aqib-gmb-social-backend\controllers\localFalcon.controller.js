const logger = require("../utils/logger");
const LocalFalcon = require("../models/localFalcon.models");
const LocalFalconScanHistoryModel = require("../models/localFalconScanHistory.model");
const axios = require("axios");
const FormData = require("form-data");

// Local Falcon API configuration
const LOCAL_FALCON_API_KEY =
  process.env.LOCAL_FALCON_API_KEY || "60050e74b5cdadf386974d2752000889";
const LOCAL_FALCON_BASE_URL = "https://api.localfalcon.com/v1";

// Google Places API configuration
const GOOGLE_PLACES_API_KEY = process.env.GOOGLE_PLACES_API_KEY;
const GOOGLE_PLACES_BASE_URL = "https://maps.googleapis.com/maps/api/place";

/**
 * Helper function to fetch location details from Google Places API
 */
const fetchLocationDetails = async (placeId, requestId) => {
  if (!placeId || !GOOGLE_PLACES_API_KEY) {
    return {
      locationAddress: null,
      locationCity: null,
      locationState: null,
      locationCountry: null,
    };
  }

  try {
    logger.info("Fetching location details from Google Places API", {
      requestId,
      placeId,
    });

    const response = await axios.get(`${GOOGLE_PLACES_BASE_URL}/details/json`, {
      params: {
        place_id: placeId,
        key: GOOGLE_PLACES_API_KEY,
        fields: "formatted_address,address_components",
        language: "en",
      },
      timeout: 5000,
    });

    if (response.data.status === "OK" && response.data.result) {
      const place = response.data.result;
      const addressComponents = place.address_components || [];

      // Extract location details from address components
      let city = null;
      let state = null;
      let country = null;

      addressComponents.forEach((component) => {
        if (component.types.includes("locality")) {
          city = component.long_name;
        } else if (component.types.includes("administrative_area_level_1")) {
          state = component.long_name;
        } else if (component.types.includes("country")) {
          country = component.long_name;
        }
      });

      logger.info("Location details fetched successfully", {
        requestId,
        placeId,
        city,
        state,
        country,
      });

      return {
        locationAddress: place.formatted_address || null,
        locationCity: city,
        locationState: state,
        locationCountry: country,
      };
    } else {
      logger.warn("Google Places API returned non-OK status", {
        requestId,
        placeId,
        status: response.data.status,
      });
      return {
        locationAddress: null,
        locationCity: null,
        locationState: null,
        locationCountry: null,
      };
    }
  } catch (error) {
    logger.error("Error fetching location details from Google Places API", {
      requestId,
      placeId,
      error: error.message,
    });
    return {
      locationAddress: null,
      locationCity: null,
      locationState: null,
      locationCountry: null,
    };
  }
};

/**
 * Welcome message for Local Falcon API
 */
const welcome = async (req, res) => {
  try {
    res.status(200).json({
      success: true,
      message: "Local Falcon API is running",
      version: "1.0.0",
    });
  } catch (error) {
    logger.error("Error in welcome:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

/**
 * Calculate grid points from base coordinate
 */
const calculateGrid = async (req, res) => {
  try {
    const { lat, lng, gridSize, radius, unit } = req.body;

    logger.info("Calculating Local Falcon grid", {
      requestId: req.requestId,
      lat,
      lng,
      gridSize,
      radius,
      unit,
    });

    // Validate input
    if (!lat || !lng || !gridSize || !radius || !unit) {
      return res.status(400).json({
        success: false,
        message:
          "Missing required parameters: lat, lng, gridSize, radius, unit",
      });
    }

    // Parse grid size (e.g., "5x5" -> 5)
    const gridSizeNum = parseInt(gridSize.split("x")[0]);
    if (isNaN(gridSizeNum) || gridSizeNum < 3 || gridSizeNum > 21) {
      return res.status(400).json({
        success: false,
        message: "Invalid grid size. Must be between 3x3 and 21x21",
      });
    }

    // Convert radius to degrees based on unit
    let radiusInDegrees;
    switch (unit) {
      case "meters":
        radiusInDegrees = radius / 111320; // Approximate meters per degree
        break;
      case "kilometers":
        radiusInDegrees = radius / 111.32; // Approximate km per degree
        break;
      case "miles":
        radiusInDegrees = radius / 69.17; // Approximate miles per degree
        break;
      default:
        return res.status(400).json({
          success: false,
          message: "Invalid unit. Must be 'meters', 'kilometers', or 'miles'",
        });
    }

    // Generate grid points
    const gridPoints = generateGridPoints(
      lat,
      lng,
      gridSizeNum,
      radiusInDegrees
    );

    res.status(200).json({
      success: true,
      data: {
        gridPoints,
        center: { lat, lng },
        configuration: { gridSize, radius, unit },
      },
    });
  } catch (error) {
    logger.error("Error calculating grid:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });
    res.status(500).json({
      success: false,
      message: "Failed to calculate grid",
    });
  }
};

/**
 * Get location suggestions for autocomplete using Local Falcon API
 */
const getLocationSuggestions = async (req, res) => {
  try {
    const { query, near } = req.query;

    logger.info("Getting location suggestions from Local Falcon API", {
      requestId: req.requestId,
      query,
      near,
    });

    if (!query || query.length < 3) {
      return res.status(200).json({
        success: true,
        data: [],
      });
    }

    try {
      // Create form data for Local Falcon API
      const formData = new FormData();
      formData.append("api_key", LOCAL_FALCON_API_KEY);
      formData.append("query", query);
      if (near) {
        formData.append("near", near);
      }

      // Call Local Falcon API
      const response = await axios.post(
        `${LOCAL_FALCON_BASE_URL}/places/`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
          },
          timeout: 10000, // 10 second timeout
        }
      );

      if (
        response.data &&
        response.data.success &&
        response.data.data &&
        response.data.data.suggestions
      ) {
        // Transform Local Falcon response to our format
        const suggestions = response.data.data.suggestions.map(
          (suggestion) => ({
            place_id: suggestion.place_id,
            name: suggestion.name,
            address: suggestion.address,
            formatted_address: suggestion.address,
            lat: suggestion.lat,
            lng: suggestion.lng,
            rating: suggestion.rating,
            reviews: suggestion.reviews,
            vicinity: suggestion.address,
            description: suggestion.name,
            sab: suggestion.sab,
            map_link: suggestion.map_link,
          })
        );

        logger.info("Local Falcon API response received", {
          requestId: req.requestId,
          count: suggestions.length,
        });

        return res.status(200).json({
          success: true,
          data: suggestions,
          source: "local_falcon",
        });
      }
    } catch (apiError) {
      logger.error("Local Falcon API call failed", {
        requestId: req.requestId,
        error: apiError.message,
      });

      return res.status(500).json({
        success: false,
        message: "Failed to get location suggestions from Local Falcon API",
        error: apiError.message,
      });
    }

    // If we reach here, the API response was not in expected format
    return res.status(500).json({
      success: false,
      message: "Invalid response format from Local Falcon API",
    });
  } catch (error) {
    logger.error("Error getting location suggestions:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });
    res.status(500).json({
      success: false,
      message: "Failed to get location suggestions",
    });
  }
};

/**
 * Search for Google My Business locations using Local Falcon API
 */
const searchPlaces = async (req, res) => {
  try {
    const { query, lat, lng, radius, unit, near } = req.body;

    logger.info("Searching places with Local Falcon API", {
      requestId: req.requestId,
      query,
      lat,
      lng,
      radius,
      unit,
      near,
    });

    try {
      // Create form data for Local Falcon API
      const formData = new FormData();
      formData.append("api_key", LOCAL_FALCON_API_KEY);
      formData.append("query", query);

      // Use 'near' parameter if provided, otherwise construct from lat/lng
      if (near) {
        formData.append("near", near);
      } else if (lat && lng) {
        formData.append("near", `${lat},${lng}`);
      }

      // Call Local Falcon API
      const response = await axios.post(
        `${LOCAL_FALCON_BASE_URL}/places/`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
          },
          timeout: 10000, // 10 second timeout
        }
      );

      if (
        response.data &&
        response.data.success &&
        response.data.data &&
        response.data.data.suggestions
      ) {
        // Transform Local Falcon response to our format
        const places = response.data.data.suggestions.map((suggestion) => ({
          name: suggestion.name,
          placeId: suggestion.place_id,
          address: suggestion.address,
          rating: parseFloat(suggestion.rating) || 0,
          totalRatings: suggestion.reviews || 0,
          lat: suggestion.lat,
          lng: suggestion.lng,
          isServiceAreaBusiness: suggestion.sab || false,
          map_link: suggestion.map_link,
        }));

        logger.info("Local Falcon places API response received", {
          requestId: req.requestId,
          count: places.length,
        });

        return res.status(200).json({
          success: true,
          data: places,
          source: "local_falcon",
        });
      }
    } catch (apiError) {
      logger.error("Local Falcon places API call failed", {
        requestId: req.requestId,
        error: apiError.message,
      });

      return res.status(500).json({
        success: false,
        message: "Failed to search places using Local Falcon API",
        error: apiError.message,
      });
    }

    // If we reach here, the API response was not in expected format
    return res.status(500).json({
      success: false,
      message: "Invalid response format from Local Falcon API",
    });
  } catch (error) {
    logger.error("Error searching places:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });
    res.status(500).json({
      success: false,
      message: "Failed to search places",
    });
  }
};

/**
 * Get business ranking at specific coordinate point
 */
const getBusinessRanking = async (req, res) => {
  try {
    const { keyword, businessName, lat, lng, placeId } = req.body;

    logger.info("Getting business ranking", {
      requestId: req.requestId,
      keyword,
      businessName,
      lat,
      lng,
      placeId,
    });

    // This function requires Local Falcon API integration for real ranking data
    logger.error(
      "Business ranking functionality not implemented with Local Falcon API",
      {
        requestId: req.requestId,
      }
    );

    res.status(501).json({
      success: false,
      message:
        "Business ranking functionality not implemented with Local Falcon API",
    });
  } catch (error) {
    logger.error("Error getting business ranking:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });
    res.status(500).json({
      success: false,
      message: "Failed to get business ranking",
    });
  }
};

/**
 * Perform keyword search at specific coordinate point
 */
const performSearch = async (req, res) => {
  try {
    const { query, lat, lng, radius, unit } = req.body;

    logger.info("Performing search", {
      requestId: req.requestId,
      query,
      lat,
      lng,
      radius,
      unit,
    });

    // This function requires Local Falcon API integration for real search data
    logger.error("Search functionality not implemented with Local Falcon API", {
      requestId: req.requestId,
    });

    res.status(501).json({
      success: false,
      message: "Search functionality not implemented with Local Falcon API",
    });
  } catch (error) {
    logger.error("Error performing search:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });
    res.status(500).json({
      success: false,
      message: "Failed to perform search",
    });
  }
};

/**
 * Generate keyword suggestions using AI based on business information
 */
const generateKeywordSuggestions = async (req, res) => {
  try {
    const { businessName, businessCategory, location, businessType } = req.body;

    logger.info("Generating keyword suggestions", {
      requestId: req.requestId,
      businessName,
      businessCategory,
      location,
      businessType,
    });

    // Validate required fields
    if (!businessName) {
      return res.status(400).json({
        success: false,
        message: "Business name is required for keyword suggestions",
      });
    }

    // Build AI prompt for keyword generation
    let prompt = `Generate relevant Local SEO keywords for a business with the following details:

Business Name: ${businessName}`;

    if (businessCategory) {
      prompt += `\nBusiness Category: ${businessCategory}`;
    }

    if (businessType) {
      prompt += `\nBusiness Type: ${businessType}`;
    }

    if (location) {
      prompt += `\nLocation: ${location}`;
    }

    prompt += `

Requirements:
- Generate 10-15 relevant keywords that customers would search for
- Include both service-based and location-based keywords
- Focus on keywords that would help with local search rankings
- Include variations with and without location
- Make keywords practical and commonly searched
- Return only the keywords, one per line
- No descriptions or explanations, just the keywords

Example format:
keyword 1
keyword 2
keyword 3

Generate keywords for this business:`;

    // Use the existing AI service to generate keywords
    const Reviews = require("../models/reviews.models");
    const generatedKeywords = await Reviews.getReplyFromAI(prompt, 5);

    if (!generatedKeywords) {
      return res.status(500).json({
        success: false,
        message: "Failed to generate keyword suggestions",
      });
    }

    // Parse the generated keywords into an array
    const keywordList = generatedKeywords
      .split("\n")
      .map((keyword) => keyword.trim())
      .filter((keyword) => keyword.length > 0 && !keyword.includes(":"))
      .slice(0, 15); // Limit to 15 keywords

    logger.info("Keyword suggestions generated successfully", {
      requestId: req.requestId,
      keywordCount: keywordList.length,
    });

    res.status(200).json({
      success: true,
      message: "Keyword suggestions generated successfully",
      data: {
        keywords: keywordList,
        businessName,
        businessCategory,
        location,
      },
    });
  } catch (error) {
    logger.error("Error generating keyword suggestions:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });

    res.status(500).json({
      success: false,
      message: "Failed to generate keyword suggestions",
      error: error.message,
    });
  }
};

/**
 * Run a full grid scan
 */
const runGridScan = async (req, res) => {
  try {
    const { keyword, businessName, lat, lng, gridSize, radius, unit, placeId } =
      req.body;

    logger.info("Running grid scan", {
      requestId: req.requestId,
      keyword,
      businessName,
      lat,
      lng,
      gridSize,
      radius,
      unit,
      placeId,
    });

    // Validate required parameters
    if (!keyword || !lat || !lng || !gridSize || !radius) {
      return res.status(400).json({
        success: false,
        message:
          "Missing required parameters: keyword, lat, lng, gridSize, radius",
      });
    }

    // Convert unit to Local Falcon API format
    let measurement = unit;
    if (unit === "kilometers") {
      measurement = "km";
    } else if (unit === "meters") {
      measurement = "m";
    } else if (unit === "miles") {
      measurement = "mi";
    }

    // Convert grid size to Local Falcon API format
    // Frontend sends "5x5", API expects "5"
    let gridSizeNumber = gridSize;
    if (typeof gridSize === "string" && gridSize.includes("x")) {
      gridSizeNumber = gridSize.split("x")[0];
    }

    // Validate grid size is supported by Local Falcon API
    const validGridSizes = [
      "3",
      "5",
      "7",
      "9",
      "11",
      "13",
      "15",
      "17",
      "19",
      "21",
    ];
    if (!validGridSizes.includes(gridSizeNumber)) {
      return res.status(400).json({
        success: false,
        message: `Invalid grid size: ${gridSize}. Valid sizes are: ${validGridSizes.join(
          ", "
        )}`,
      });
    }

    // Create form data for Local Falcon API
    const formData = new FormData();
    formData.append("api_key", LOCAL_FALCON_API_KEY);
    formData.append("keyword", keyword);
    formData.append("lat", lat.toString());
    formData.append("lng", lng.toString());
    formData.append("grid_size", gridSizeNumber);
    formData.append("radius", radius.toString());
    formData.append("measurement", measurement);

    if (placeId) {
      formData.append("place_id", placeId);
    }

    logger.info("Calling Local Falcon scan API", {
      requestId: req.requestId,
      endpoint: `${LOCAL_FALCON_BASE_URL}/scan/`,
      parameters: {
        keyword,
        lat,
        lng,
        grid_size: gridSizeNumber, // Show the converted value
        original_grid_size: gridSize, // Show the original value
        radius,
        measurement,
        original_unit: unit, // Show the original unit
        place_id: placeId || "not provided",
      },
    });

    // Call Local Falcon API
    const response = await axios.post(
      `${LOCAL_FALCON_BASE_URL}/scan/`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
        },
        timeout: 60000, // 60 second timeout for scan operations
      }
    );

    logger.info("Local Falcon scan API response", {
      requestId: req.requestId,
      status: response.status,
      dataKeys: Object.keys(response.data || {}),
    });

    if (response.data && response.status === 200) {
      // Transform the Local Falcon response to match our expected format
      // The actual data is nested under response.data.data
      const scanResult = {
        keyword,
        businessName,
        gridConfiguration: {
          lat,
          lng,
          gridSize,
          radius,
          unit: measurement,
        },
        gridPoints: response.data.data?.results || [],
        rankings: response.data.data?.results || [],
        averagePosition: response.data.data?.arp || 0,
        visibilityPercentage: response.data.data?.percent || 0,
        totalSearches: response.data.data?.points || 0,
        foundInResults: response.data.data?.found || 0,
        scanId: response.data.data?.scan_id || null,
        rawResponse: response.data, // Include raw response for debugging
      };

      // Save scan history to database
      try {
        // Debug user object to understand structure
        logger.info("User object for scan history", {
          requestId: req.requestId,
          user: req.user,
          userKeys: req.user ? Object.keys(req.user) : "no user object",
        });

        // Get user ID from JWT token (it's stored as 'userId' and is a string)
        const userId = req.user?.userId ? parseInt(req.user.userId) : null;

        if (!userId) {
          logger.error("No user ID found for scan history", {
            requestId: req.requestId,
            userObject: req.user,
            bodyUserId: req.body.userId,
          });
        }

        // Fetch location details if placeId is provided
        const locationDetails = await fetchLocationDetails(
          placeId,
          req.requestId
        );

        const scanHistoryData = {
          userId: userId,
          businessName,
          keyword,
          placeId,
          latitude: lat,
          longitude: lng,
          gridSize: gridSizeNumber,
          radiusKm:
            measurement === "km"
              ? radius
              : measurement === "mi"
              ? radius * 1.60934
              : radius / 1000,
          locationName: businessName,
          locationAddress: locationDetails.locationAddress,
          locationCity: locationDetails.locationCity,
          locationState: locationDetails.locationState,
          locationCountry: locationDetails.locationCountry,
          // Fix the field mapping to match Local Falcon API response structure
          // The actual data is nested under response.data.data
          // Ensure numeric values are properly converted
          totalPoints: parseInt(response.data.data?.points || 0),
          foundPoints: parseInt(response.data.data?.found || 0),
          visibilityPercent: parseFloat(response.data.data?.percent || 0),
          averageRankingPosition: parseFloat(response.data.data?.arp || 0),
          averageTopRankingPosition: parseFloat(response.data.data?.atrp || 0),
          shareOfLocalVoice: parseFloat(response.data.data?.solv || 0),
          requestData: {
            keyword,
            businessName,
            lat,
            lng,
            gridSize,
            radius,
            unit,
            placeId,
            measurement,
          },
          responseData: response.data,
          scanStatus: "completed",
          scanDurationMs: null, // Could be calculated if needed
          errorMessage: null,
        };

        // Debug the response data to understand the structure
        logger.info("Local Falcon API response data for debugging", {
          requestId: req.requestId,
          responseDataKeys: Object.keys(response.data || {}),
          responseData: response.data,
          actualDataFromAPI: response.data.data, // Show the nested data
          locationDetails: locationDetails,
          scanHistoryData: {
            totalPoints: scanHistoryData.totalPoints,
            foundPoints: scanHistoryData.foundPoints,
            visibilityPercent: scanHistoryData.visibilityPercent,
            averageRankingPosition: scanHistoryData.averageRankingPosition,
            locationAddress: scanHistoryData.locationAddress,
            locationCity: scanHistoryData.locationCity,
            locationState: scanHistoryData.locationState,
            locationCountry: scanHistoryData.locationCountry,
          },
        });

        debugger;
        await LocalFalconScanHistoryModel.create(scanHistoryData);
        debugger;
        logger.info("Scan history saved successfully", {
          requestId: req.requestId,
          userId: scanHistoryData.userId,
          keyword,
          businessName,
        });
      } catch (historyError) {
        logger.error("Failed to save scan history", {
          requestId: req.requestId,
          error: historyError.message,
          stack: historyError.stack,
        });
        // Don't fail the main request if history saving fails
      }

      res.status(200).json({
        success: true,
        data: scanResult,
        message: "Grid scan completed successfully",
      });
    } else {
      logger.error("Invalid response from Local Falcon API", {
        requestId: req.requestId,
        status: response.status,
        data: response.data,
      });

      res.status(500).json({
        success: false,
        message: "Invalid response from Local Falcon API",
      });
    }
  } catch (error) {
    logger.error("Error running grid scan:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
      isAxiosError: error.isAxiosError,
      response: error.response
        ? {
            status: error.response.status,
            data: error.response.data,
          }
        : null,
    });

    if (error.response) {
      // Local Falcon API returned an error
      res.status(error.response.status || 500).json({
        success: false,
        message: error.response.data?.message || "Local Falcon API error",
        error: error.response.data,
      });
    } else if (error.code === "ECONNABORTED") {
      // Timeout error
      res.status(408).json({
        success: false,
        message: "Grid scan request timed out. Please try again.",
      });
    } else {
      // Other errors
      res.status(500).json({
        success: false,
        message: "Failed to run grid scan",
        error: error.message,
      });
    }
  }
};

/**
 * Helper function to generate grid points
 */
function generateGridPoints(centerLat, centerLng, gridSize, radiusInDegrees) {
  const points = [];
  const latOffset = (radiusInDegrees * 2) / (gridSize - 1);
  const lngOffset = (radiusInDegrees * 2) / (gridSize - 1);

  const halfGrid = Math.floor(gridSize / 2);

  for (let i = 0; i < gridSize; i++) {
    for (let j = 0; j < gridSize; j++) {
      const latIndex = i - halfGrid;
      const lngIndex = j - halfGrid;

      const lat = centerLat + latIndex * latOffset;
      const lng = centerLng + lngIndex * lngOffset;

      points.push({
        lat: parseFloat(lat.toFixed(6)),
        lng: parseFloat(lng.toFixed(6)),
        index: i * gridSize + j,
        gridPosition: { row: i, col: j },
      });
    }
  }

  return points;
}

/**
 * Save Local Falcon configuration
 */
const saveConfiguration = async (req, res) => {
  try {
    const configData = req.body;

    logger.info("Saving Local Falcon configuration", {
      requestId: req.requestId,
      userId: configData.userId,
      name: configData.name,
    });

    const result = await LocalFalcon.saveConfiguration(configData);

    res.status(200).json({
      success: true,
      data: result,
      message: "Configuration saved successfully",
    });
  } catch (error) {
    logger.error("Error saving configuration:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });
    res.status(500).json({
      success: false,
      message: "Failed to save configuration",
    });
  }
};

/**
 * Get all configurations for user
 */
const getConfigurations = async (req, res) => {
  try {
    const { userId } = req.query;

    logger.info("Getting Local Falcon configurations", {
      requestId: req.requestId,
      userId,
    });

    const configurations = await LocalFalcon.getConfigurations(userId);

    res.status(200).json({
      success: true,
      data: configurations,
    });
  } catch (error) {
    logger.error("Error getting configurations:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });
    res.status(500).json({
      success: false,
      message: "Failed to get configurations",
    });
  }
};

/**
 * Get specific configuration by ID
 */
const getConfiguration = async (req, res) => {
  try {
    const { configId } = req.params;

    logger.info("Getting Local Falcon configuration", {
      requestId: req.requestId,
      configId,
    });

    const configuration = await LocalFalcon.getConfiguration(configId);

    if (!configuration) {
      return res.status(404).json({
        success: false,
        message: "Configuration not found",
      });
    }

    res.status(200).json({
      success: true,
      data: configuration,
    });
  } catch (error) {
    logger.error("Error getting configuration:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });
    res.status(500).json({
      success: false,
      message: "Failed to get configuration",
    });
  }
};

/**
 * Update configuration
 */
const updateConfiguration = async (req, res) => {
  try {
    const { configId } = req.params;
    const updateData = req.body;

    logger.info("Updating Local Falcon configuration", {
      requestId: req.requestId,
      configId,
    });

    const result = await LocalFalcon.updateConfiguration(configId, updateData);

    res.status(200).json({
      success: true,
      data: result,
      message: "Configuration updated successfully",
    });
  } catch (error) {
    logger.error("Error updating configuration:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });
    res.status(500).json({
      success: false,
      message: "Failed to update configuration",
    });
  }
};

/**
 * Delete configuration
 */
const deleteConfiguration = async (req, res) => {
  try {
    const { configId } = req.params;

    logger.info("Deleting Local Falcon configuration", {
      requestId: req.requestId,
      configId,
    });

    await LocalFalcon.deleteConfiguration(configId);

    res.status(200).json({
      success: true,
      message: "Configuration deleted successfully",
    });
  } catch (error) {
    logger.error("Error deleting configuration:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });
    res.status(500).json({
      success: false,
      message: "Failed to delete configuration",
    });
  }
};

/**
 * Get scan history for the current user
 */
const getScanHistory = async (req, res) => {
  try {
    const userId = req.user?.userId ? parseInt(req.user.userId) : null;
    const {
      page = 1,
      limit = 20,
      sortBy = "created_at",
      sortOrder = "DESC",
      status,
      keyword,
      businessName,
    } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      status,
      keyword,
      businessName,
    };

    const result = await LocalFalconScanHistoryModel.getByUserId(
      userId,
      options
    );

    res.status(200).json({
      success: true,
      data: result.data,
      pagination: result.pagination,
      message: "Scan history retrieved successfully",
    });
  } catch (error) {
    logger.error("Error getting scan history:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });

    res.status(500).json({
      success: false,
      message: "Failed to retrieve scan history",
      error: error.message,
    });
  }
};

/**
 * Get all scan history (admin only)
 */
const getAllScanHistory = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      sortBy = "created_at",
      sortOrder = "DESC",
      status,
      keyword,
      businessName,
      userId,
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      status,
      keyword,
      businessName,
      userId: userId ? parseInt(userId) : null,
    };

    const result = await LocalFalconScanHistoryModel.getAll(options);

    res.status(200).json({
      success: true,
      data: result.data,
      pagination: result.pagination,
      message: "All scan history retrieved successfully",
    });
  } catch (error) {
    logger.error("Error getting all scan history:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });

    res.status(500).json({
      success: false,
      message: "Failed to retrieve scan history",
      error: error.message,
    });
  }
};

/**
 * Get scan history by ID with full response data
 */
const getScanHistoryById = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.userId ? parseInt(req.user.userId) : null;
    const isAdmin = req.user?.role === "admin" || req.user?.isAdmin;

    if (!id) {
      return res.status(400).json({
        success: false,
        message: "Scan history ID is required",
      });
    }

    // If not admin, restrict to user's own records
    const scanHistory = await LocalFalconScanHistoryModel.getById(
      parseInt(id),
      isAdmin ? null : userId
    );

    if (!scanHistory) {
      return res.status(404).json({
        success: false,
        message: "Scan history not found",
      });
    }

    res.status(200).json({
      success: true,
      data: scanHistory,
      message: "Scan history retrieved successfully",
    });
  } catch (error) {
    logger.error("Error getting scan history by ID:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
      scanId: req.params.id,
    });

    res.status(500).json({
      success: false,
      message: "Failed to retrieve scan history",
      error: error.message,
    });
  }
};

/**
 * Delete scan history record
 */
const deleteScanHistory = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.userId ? parseInt(req.user.userId) : null;
    const isAdmin = req.user?.role === "admin" || req.user?.isAdmin;

    if (!id) {
      return res.status(400).json({
        success: false,
        message: "Scan history ID is required",
      });
    }

    // If not admin, restrict to user's own records
    const deleted = await LocalFalconScanHistoryModel.delete(
      parseInt(id),
      isAdmin ? null : userId
    );

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: "Scan history not found or not authorized to delete",
      });
    }

    res.status(200).json({
      success: true,
      message: "Scan history deleted successfully",
    });
  } catch (error) {
    logger.error("Error deleting scan history:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
      scanId: req.params.id,
    });

    res.status(500).json({
      success: false,
      message: "Failed to delete scan history",
      error: error.message,
    });
  }
};

module.exports = {
  welcome,
  calculateGrid,
  getLocationSuggestions,
  searchPlaces,
  getBusinessRanking,
  performSearch,
  generateKeywordSuggestions,
  runGridScan,
  saveConfiguration,
  getConfigurations,
  getConfiguration,
  updateConfiguration,
  deleteConfiguration,
  getScanHistory,
  getAllScanHistory,
  getScanHistoryById,
  deleteScanHistory,
};

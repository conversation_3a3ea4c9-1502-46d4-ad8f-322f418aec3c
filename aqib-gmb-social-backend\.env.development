## Please note that all environment variables should have the prefix 'APP' for their names. ##

# ################# REQUIRED ENV VARS START #################
PREFIX=APP
APP_PORT=3000
APP_ENV_NAME=DEVELOPMENT
APP_VER_PREFIX=v1
APP_LOG_LEVEL=INFO
# ################# REQUIRED ENV VARS END #################

# ################# DATABASE ENV VARS START #################
APP_DB_HOST=aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com
APP_DB_USER=admin
APP_DB_PASSWORD=lAverOpERiaN
APP_DB_NAME=gmb
# ################# DATABASE ENV VARS END #################

# ################# AWS S3 ENV VARS START #################
APP_AWS_ACCESS_KEY_ID=********************
APP_AWS_SECRET_ACCESS_KEY=h9O3B/VDX4/5CrgM36zTA/+5GOTksjCy768l9r2H
APP_AWS_REGION=us-east-1
APP_AWS_S3_BUCKET=gmb-social-assets

# S3 URL Configuration
# Set to 'true' to use public URLs instead of signed URLs (requires public bucket or proper bucket policy)
S3_USE_PUBLIC_URLS=false
# ################# AWS S3 ENV VARS END #################

APP_JWT_SECRET_KEY=somesupersecretsecret

APP_GOOGLE_GENAI_KEY=AIzaSyDQdb4FiXa1tY04omim38Z5rDt79A_3u1s

APP_PAGINATION_MAX_LIMIT=5

# ################# FACEBOOK INTEGRATION ENV VARS START #################
# Facebook App Configuration (replace with your actual Facebook app credentials)
# FACEBOOK_CLIENT_ID=1242446240837700
# FACEBOOK_CLIENT_SECRET=********************************
FACEBOOK_CLIENT_ID=2720684881469467
FACEBOOK_CLIENT_SECRET=********************************
FACEBOOK_REDIRECT_URI=http://localhost:3000/v1/facebook/callback

# Frontend URL for redirects after authentication
FRONTEND_URL=http://localhost:5173
UI_ORIGIN=http://localhost:5173
ALLOW_CORS_URLS=http://localhost:5173,http://***********:5173/

# Facebook API Version (using latest v20.0)
FACEBOOK_API_VERSION=v20.0
# ################# FACEBOOK INTEGRATION ENV VARS END #################

# ################# INSTAGRAM INTEGRATION ENV VARS START #################
# Instagram App Configuration (uses Facebook app credentials for Instagram Basic Display API)
INSTAGRAM_CLIENT_ID=1242446240837700
INSTAGRAM_CLIENT_SECRET=********************************
INSTAGRAM_REDIRECT_URI=http://localhost:3000/v1/instagram/callback

# Instagram API Version (using latest v20.0)
INSTAGRAM_API_VERSION=v20.0
# ################# INSTAGRAM INTEGRATION ENV VARS END #################

# ################# LINKEDIN INTEGRATION ENV VARS START #################
# LinkedIn App Configuration
LINKEDIN_CLIENT_ID=7736hsd2hmgkaq
LINKEDIN_CLIENT_SECRET=WPL_AP1.qhLMCMu9DjduTT3q.AaEHUQ==
LINKEDIN_REDIRECT_URI=http://localhost:3000/v1/linkedin/callback

# LinkedIn API Version
LINKEDIN_API_VERSION=v2
# ################# LINKEDIN INTEGRATION ENV VARS END #################

# ################# TWITTER INTEGRATION ENV VARS START #################
# Twitter App Configuration (replace with your actual Twitter app credentials)
TWITTER_CLIENT_ID=dTZ6T0g3U2hqSXRsR25scTdRd246MTpjaQ
TWITTER_CLIENT_SECRET=XZWMsbEiKVYCoqnYKz8Utw2ELZJbhw8m6jrN-rkRoW_28NCUXC
TWITTER_REDIRECT_URI=http://***********:5173/business-management/twitter/callback

# Twitter API Version (using v2)
TWITTER_API_VERSION=v2
# ################# TWITTER INTEGRATION ENV VARS END #################

# ################# LOCAL FALCON INTEGRATION ENV VARS START #################
# Google Places API Configuration (for Local Falcon ranking checks)
GOOGLE_PLACES_API_KEY=AIzaSyB8OKKYB2phgpBCJ2UfP6UmxHyTPxJNpCI

# Local Falcon Configuration
LOCAL_FALCON_DEFAULT_RADIUS=5000
LOCAL_FALCON_MAX_RESULTS=25
LOCAL_FALCON_RATE_LIMIT_DELAY=100
# ################# LOCAL FALCON INTEGRATION ENV VARS END #################

# HTTPS Configuration
HTTPS=true
HTTPS_PORT=3443
SSL_KEY_PATH=./ssl/private-key.pem
SSL_CERT_PATH=./ssl/certificate.pem

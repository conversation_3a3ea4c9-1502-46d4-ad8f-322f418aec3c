import React, { useState } from "react";
import {
  Box,
  TextField,
  FormControlLabel,
  Switch,
  <PERSON>ton,
  <PERSON>pography,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  InputAdornment,
} from "@mui/material";
import { LocalizationProvider, DateTimePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";
import DeleteIcon from "@mui/icons-material/Delete";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import PhotoLibraryIcon from "@mui/icons-material/PhotoLibrary";
import AutoFixHighIcon from "@mui/icons-material/AutoFixHigh";
import {
  IFacebookCreatePost,
  IFacebookSelectedPage,
} from "../../../interfaces/request/IFacebookCreatePost";
import { IFacebookPageData } from "../../../interfaces/response/IFacebookCreatePostResponse";

interface FacebookPostFormProps {
  formData: IFacebookCreatePost;
  onFormChange: (data: IFacebookCreatePost) => void;
  uploadedImages: any[];
  onImageUpload: () => void;
  onGalleryOpen: () => void;
  onImageRemove: (index: number) => void;
  errors?: any;
  onSubmit?: () => void;
  isFacebookConnected?: boolean;
}

const FacebookPostForm: React.FC<FacebookPostFormProps> = ({
  formData,
  onFormChange,
  uploadedImages,
  onImageUpload,
  onGalleryOpen,
  onImageRemove,
  errors,
  onSubmit,
  isFacebookConnected = true,
}) => {
  const [scheduleForLater, setScheduleForLater] = useState(false);
  const [scheduledDate, setScheduledDate] = useState<Dayjs | null>(
    dayjs().add(1, "hour")
  );

  const handleMessageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onFormChange({
      ...formData,
      message: event.target.value,
    });
  };

  const handleLinkChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onFormChange({
      ...formData,
      link: event.target.value,
    });
  };

  const handleScheduleToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    const isScheduled = event.target.checked;
    setScheduleForLater(isScheduled);

    onFormChange({
      ...formData,
      published: !isScheduled,
      scheduledPublishTime:
        isScheduled && scheduledDate ? scheduledDate.toISOString() : undefined,
    });
  };

  const handleDateChange = (newDate: Dayjs | null) => {
    setScheduledDate(newDate);
    if (scheduleForLater && newDate) {
      onFormChange({
        ...formData,
        scheduledPublishTime: newDate.toISOString(),
      });
    }
  };

  // Helper function to add {Page Name} placeholder
  const addPageNamePlaceholder = () => {
    const currentValue = formData.message;
    const newValue = currentValue + (currentValue ? " " : "") + "{Page Name}";

    onFormChange({
      ...formData,
      message: newValue,
    });
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Facebook Post Details
        </Typography>

        <Grid container spacing={3}>
          {/* Post Message */}
          <Grid item xs={12}>
            <Box sx={{ position: "relative" }}>
              <TextField
                fullWidth
                multiline
                rows={4}
                label="Title"
                placeholder="What's on your mind? Use {Page Name} to dynamically replace with each page's name."
                value={formData.message}
                onChange={handleMessageChange}
                error={!!errors?.message}
                helperText={
                  errors?.message ||
                  `${formData.message.length}/2200 characters. Tip: Use {Page Name} for dynamic replacement.`
                }
                inputProps={{ maxLength: 2200 }}
                variant="filled"
                sx={{
                  backgroundColor: "var(--whiteColor)",
                  borderRadius: "5px",
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => addPageNamePlaceholder()}
                        size="small"
                        title="Add {Page Name} placeholder"
                        sx={{
                          color: "primary.main",
                          "&:hover": {
                            backgroundColor: "primary.light",
                            color: "white",
                          },
                        }}
                      >
                        <AutoFixHighIcon fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
          </Grid>

          {/* Link (optional) */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Link (Optional)"
              placeholder="https://example.com"
              value={formData.link || ""}
              onChange={handleLinkChange}
              variant="filled"
              sx={{
                backgroundColor: "var(--whiteColor)",
                borderRadius: "5px",
              }}
            />
          </Grid>

          {/* Media Upload Section */}
          <Grid item xs={12}>
            <Box>
              <Box
                onClick={onGalleryOpen}
                sx={{
                  border: "2px dashed #e0e0e0",
                  borderRadius: 2,
                  p: 4,
                  textAlign: "center",
                  cursor: "pointer",
                  backgroundColor: "#fafafa",
                  transition: "all 0.3s ease",
                  "&:hover": {
                    borderColor: "#1976d2",
                    backgroundColor: "#f5f5f5",
                  },
                }}
              >
                <CloudUploadIcon
                  sx={{
                    fontSize: 48,
                    color: "#9e9e9e",
                    mb: 1,
                  }}
                />
                <Typography variant="h6" sx={{ mb: 0.5, color: "#666" }}>
                  Add/Edit Post Media
                </Typography>
                <Typography variant="caption" sx={{ color: "#999" }}>
                  Supported: Images (JPG, PNG, GIF, WebP) and Videos (MP4, AVI,
                  MOV, WMV, FLV, WebM)
                </Typography>
              </Box>
            </Box>
          </Grid>

          {/* Schedule for Later */}
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={scheduleForLater}
                  onChange={handleScheduleToggle}
                  color="primary"
                />
              }
              label="Schedule for later"
            />
          </Grid>

          {/* Date/Time Picker */}
          {scheduleForLater && (
            <Grid item xs={12}>
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DateTimePicker
                  label="Schedule Date & Time"
                  value={scheduledDate}
                  onChange={handleDateChange}
                  minDateTime={dayjs()}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      variant: "filled",
                      sx: {
                        backgroundColor: "var(--whiteColor)",
                        borderRadius: "5px",
                      },
                    },
                  }}
                />
              </LocalizationProvider>
            </Grid>
          )}

          {/* Submit Button */}
          {onSubmit && isFacebookConnected && (
            <Grid item xs={12}>
              <Box sx={{ mt: 2 }}>
                <Button
                  className="updatesShapeBtn"
                  onClick={onSubmit}
                  variant="contained"
                  style={{ textTransform: "capitalize" }}
                  fullWidth
                  disabled={!formData.message}
                >
                  Create Facebook Post
                </Button>
              </Box>
            </Grid>
          )}
        </Grid>
      </CardContent>
    </Card>
  );
};

export default FacebookPostForm;
